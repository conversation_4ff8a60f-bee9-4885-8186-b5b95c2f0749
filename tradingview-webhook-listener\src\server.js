const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const winston = require('winston');
const rateLimit = require('express-rate-limit');
const path = require('path');

const WebhookHandler = require('./webhook-handler');
const MexcApiClient = require('./mexc-api-client');
const MoneyManager = require('./money-manager');
const ConfigManager = require('./config-manager');
const TradingExecutor = require('./trading-executor');
const MarketDataService = require('./market-data-service');
const PositionManager = require('./position-manager');

// Configure logging
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'tradingview-webhook-listener' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        })
    ]
});

const app = express();
const PORT = process.env.PORT || 80; // Changed from 80 to 4000 for testing

// Initialize components
const configManager = new ConfigManager();
const mexcApiClient = new MexcApiClient(configManager);
const marketDataService = new MarketDataService(configManager);
const tradingExecutor = new TradingExecutor(configManager);
const moneyManager = new MoneyManager(configManager, mexcApiClient, tradingExecutor); // Pass tradingExecutor for browser balance
const positionManager = new PositionManager(configManager, marketDataService, tradingExecutor, logger);
const webhookHandler = new WebhookHandler(configManager, moneyManager, tradingExecutor, marketDataService, positionManager, logger);

// Rate limiting
const webhookLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many webhook requests from this IP'
});

const apiLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 200, // limit each IP to 200 requests per windowMs
    message: 'Too many API requests from this IP'
});

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"]
        }
    }
}));

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, '../public')));

// Request logging middleware
app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path}`, { 
        ip: req.ip, 
        userAgent: req.get('User-Agent')
    });
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        service: 'tradingview-webhook-listener',
        version: '1.0.0',
        components: {
            config: configManager.isConfigured(),
            mexcApi: mexcApiClient.isConnected(),
            tradingExecutor: tradingExecutor.isReady()
        }
    });
});

// Service info endpoint
app.get('/api/info', apiLimiter, (req, res) => {
    res.json({
        service: 'TradingView Webhook Listener',
        version: '1.0.0',
        description: 'Receives TradingView signals and executes trades with money management',
        supportedSymbols: ['TRUUSDT'],
        supportedActions: ['open', 'close'],
        features: [
            'TradingView webhook integration',
            'MEXC API integration',
            'Smart money management',
            'Percentage-based position sizing',
            'Fixed amount trading',
            'Bot activation control',
            'Real-time balance monitoring',
            'SL/TP automation with ATR',
            'Position monitoring',
            'Execution validation',
            'Responsive web interface'
        ],
        endpoints: {
            webhook: '/webhook',
            config: '/api/config',
            status: '/api/status',
            trades: '/api/trades',
            balance: '/api/balance',
            positions: '/api/positions',
            marketData: '/api/market-data'
        }
    });
});

// Main webhook endpoint
app.post('/webhook', webhookLimiter, async (req, res) => {
    try {
        const result = await webhookHandler.handleWebhook(req.body);
        res.json(result);
    } catch (error) {
        logger.error('Webhook handling failed', { error: error.message, stack: error.stack });
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Configuration endpoints
app.get('/api/config', apiLimiter, (req, res) => {
    try {
        const config = configManager.getConfig();
        // Don't send sensitive data
        const safeConfig = {
            ...config,
            mexcApiKey: config.mexcApiKey ? '***CONFIGURED***' : null,
            mexcSecretKey: config.mexcSecretKey ? '***CONFIGURED***' : null
        };
        res.json(safeConfig);
    } catch (error) {
        logger.error('Config retrieval failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/config', apiLimiter, async (req, res) => {
    try {
        const result = await configManager.updateConfig(req.body);
        
        // Reinitialize components with new config
        mexcApiClient.updateConfig(configManager);
        moneyManager.updateConfig(configManager, mexcApiClient, tradingExecutor);
        
        logger.info('Configuration updated successfully');
        res.json({ success: true, message: 'Configuration updated successfully' });
    } catch (error) {
        logger.error('Config update failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

// Status endpoint
app.get('/api/status', apiLimiter, async (req, res) => {
    try {
        // Force check connection by attempting to get balance if credentials exist
        let mexcConnected = mexcApiClient.isConnected();
        let balance = null;
        let balanceError = null;

        if (mexcApiClient.hasValidCredentials()) {
            try {
                // Try to get balance from API first
                balance = await mexcApiClient.getBalance();
                mexcConnected = true; // Connection successful

                // If API returns 0 balance, try frontend as fallback
                if (balance && (balance.free === 0 || balance.total === 0)) {
                    console.log('⚠️ API returned 0 balance, trying frontend fallback...');
                    try {
                        const frontendBalance = await tradingExecutor.getBalance();
                        if (frontendBalance.success && frontendBalance.balance > 0) {
                            console.log(`✅ Frontend balance found: ${frontendBalance.balance} ${frontendBalance.currency}`);
                            balance = {
                                asset: frontendBalance.currency,
                                free: frontendBalance.balance,
                                locked: 0,
                                total: frontendBalance.balance,
                                updateTime: frontendBalance.timestamp,
                                timestamp: frontendBalance.timestamp,
                                source: 'frontend',
                                raw: frontendBalance.raw,
                                cached: frontendBalance.cached
                            };
                        } else {
                            console.log('⚠️ Frontend balance also returned 0 or failed');
                            // Keep API balance but mark source
                            if (balance) {
                                balance.source = 'api';
                            }
                        }
                    } catch (frontendError) {
                        console.log('⚠️ Frontend balance failed:', frontendError.message);
                        // Keep API balance but mark source
                        if (balance) {
                            balance.source = 'api';
                        }
                    }
                } else if (balance) {
                    // API has non-zero balance, mark source
                    balance.source = 'api';
                }

            } catch (error) {
                mexcConnected = false;
                balanceError = error.message;

                // If API fails completely, try to get balance from frontend
                console.log('❌ API failed completely, trying frontend fallback...');
                try {
                    const frontendBalance = await tradingExecutor.getBalance();
                    if (frontendBalance.success) {
                        console.log(`✅ Frontend balance found: ${frontendBalance.balance} ${frontendBalance.currency}`);
                        balance = {
                            asset: frontendBalance.currency,
                            free: frontendBalance.balance,
                            locked: 0,
                            total: frontendBalance.balance,
                            updateTime: frontendBalance.timestamp,
                            timestamp: frontendBalance.timestamp,
                            source: 'frontend',
                            raw: frontendBalance.raw,
                            cached: frontendBalance.cached
                        };
                        mexcConnected = true; // Frontend connection successful
                        balanceError = null;
                    }
                } catch (frontendError) {
                    console.log('❌ Frontend balance also failed:', frontendError.message);
                    // Keep original API error
                }
            }
        }

        const status = {
            botActive: configManager.getConfig().botActive,
            configured: configManager.isConfigured(),
            mexcConnected,
            lastSignalTime: webhookHandler.getLastSignalTime(),
            totalSignalsReceived: webhookHandler.getTotalSignalsReceived(),
            totalTradesExecuted: webhookHandler.getTotalTradesExecuted(),
            timestamp: new Date().toISOString()
        };

        if (balance) {
            status.balance = balance;
        } else if (balanceError) {
            status.balanceError = balanceError;
        }

        res.json(status);
    } catch (error) {
        logger.error('Status retrieval failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

// Balance endpoint
app.get('/api/balance', apiLimiter, async (req, res) => {
    try {
        const forceRefresh = req.query.refresh === 'true';
        let balance = null;
        let source = 'unknown';

        // Try API first if configured
        if (mexcApiClient.isConnected()) {
            try {
                balance = await mexcApiClient.getBalance();
                source = 'api';
                logger.info(`Balance retrieved from API: ${balance.free} USDT`);
                logger.info(`Balance structure: ${JSON.stringify(balance)}`);

                // If API returns 0 balance, try frontend as fallback
                if (balance && (balance.free === 0 || balance.total === 0)) {
                    logger.warn('API returned 0 balance, trying frontend fallback...');
                    try {
                        const frontendBalance = await tradingExecutor.getBalance(forceRefresh);
                        if (frontendBalance.success && frontendBalance.balance > 0) {
                            logger.info(`Frontend balance found: ${frontendBalance.balance} ${frontendBalance.currency}`);
                            balance = {
                                asset: frontendBalance.currency,
                                free: frontendBalance.balance,
                                locked: 0,
                                total: frontendBalance.balance,
                                updateTime: frontendBalance.timestamp,
                                timestamp: frontendBalance.timestamp,
                                raw: frontendBalance.raw,
                                cached: frontendBalance.cached
                            };
                            source = 'frontend';
                        } else {
                            logger.warn('Frontend balance also returned 0 or failed');
                            // Keep API balance but note the source
                        }
                    } catch (frontendError) {
                        logger.warn('Frontend balance failed:', frontendError.message);
                        // Keep API balance
                    }
                }

            } catch (apiError) {
                logger.warn('API balance failed, trying frontend:', apiError.message);
                balance = null; // Reset to try frontend
            }
        }

        // If API failed completely or not configured, try frontend
        if (!balance) {
            try {
                const frontendBalance = await tradingExecutor.getBalance(forceRefresh);
                if (frontendBalance.success) {
                    balance = {
                        asset: frontendBalance.currency,
                        free: frontendBalance.balance,
                        locked: 0,
                        total: frontendBalance.balance,
                        updateTime: frontendBalance.timestamp,
                        timestamp: frontendBalance.timestamp,
                        raw: frontendBalance.raw,
                        cached: frontendBalance.cached
                    };
                    source = 'frontend';
                    logger.info(`Balance retrieved from frontend: ${frontendBalance.balance} ${frontendBalance.currency}`);
                } else {
                    throw new Error(frontendBalance.error || 'Frontend balance failed');
                }
            } catch (frontendError) {
                logger.error('Frontend balance failed:', frontendError.message);
                return res.status(500).json({
                    success: false,
                    error: `Both API and frontend balance failed. API: ${mexcApiClient.isConnected() ? 'configured but failed' : 'not configured'}. Frontend: ${frontendError.message}`
                });
            }
        }

        res.json({
            success: true,
            balance,
            source,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Balance retrieval failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

// Trades history endpoint
app.get('/api/trades', apiLimiter, (req, res) => {
    try {
        const trades = webhookHandler.getTradesHistory();
        res.json({ success: true, trades });
    } catch (error) {
        logger.error('Trades retrieval failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

// Test webhook endpoint
app.post('/api/test-webhook', apiLimiter, async (req, res) => {
    try {
        // Use the new TradingView format with 'leverege' typo and 'buy' action
        const testSignal = {
            symbol: "TRUUSDT",
            trade: "buy",
            last_price: "0.000012064",
            leverege: "2"
        };

        logger.info('Processing test webhook signal with new TradingView format');
        const result = await webhookHandler.handleWebhook(testSignal);
        res.json({ success: true, result });
    } catch (error) {
        logger.error('Test webhook failed', { error: error.message });
        res.status(500).json({ success: false, error: error.message });
    }
});

// Serve the main dashboard
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    logger.error('Unhandled error', { error: error.message, stack: error.stack });
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
    });
});

// Position management endpoints
app.get('/api/positions', apiLimiter, (req, res) => {
    try {
        const positions = positionManager.getActivePositions();
        const stats = positionManager.getStatistics();

        res.json({
            success: true,
            positions,
            statistics: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Failed to get positions', { error: error.message });
        res.status(500).json({
            success: false,
            error: 'Failed to get positions',
            details: error.message
        });
    }
});

// Market data endpoints
app.get('/api/market-data', apiLimiter, async (req, res) => {
    try {
        const symbol = req.query.symbol || 'TRUUSDT';
        const [currentPrice, atr] = await Promise.all([
            marketDataService.getCurrentPrice(symbol),
            marketDataService.getATR(symbol)
        ]);

        res.json({
            success: true,
            symbol,
            currentPrice,
            atr,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Failed to get market data', { error: error.message });
        res.status(500).json({
            success: false,
            error: 'Failed to get market data',
            details: error.message
        });
    }
});

app.get('/api/market-data/klines', apiLimiter, async (req, res) => {
    try {
        const symbol = req.query.symbol || 'TRUUSDT';
        const interval = req.query.interval || '1m';
        const limit = parseInt(req.query.limit) || 100;

        const klines = await marketDataService.getKlineData(symbol, interval, limit);

        res.json({
            success: true,
            symbol,
            interval,
            limit,
            klines,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Failed to get kline data', { error: error.message });
        res.status(500).json({
            success: false,
            error: 'Failed to get kline data',
            details: error.message
        });
    }
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        availableEndpoints: [
            'GET /',
            'GET /health',
            'GET /api/info',
            'POST /webhook',
            'GET /api/config',
            'POST /api/config',
            'GET /api/status',
            'GET /api/balance',
            'GET /api/trades',
            'GET /api/positions',
            'GET /api/market-data',
            'GET /api/market-data/klines',
            'POST /api/test-webhook'
        ]
    });
});

// Start server
app.listen(PORT, async () => {
    logger.info(`🚀 TradingView Webhook Listener started on port ${PORT}`);
    logger.info(`📊 Dashboard: http://localhost:${PORT}`);
    logger.info(`🔗 Webhook URL: http://localhost:${PORT}/webhook`);
    logger.info(`📋 API Info: http://localhost:${PORT}/api/info`);
    logger.info(`⚡ Ready to receive TradingView signals!`);

    // Test MEXC API connection on startup
    setTimeout(async () => {
        try {
            // Force reload configuration to ensure credentials are available
            await configManager.loadConfig();

            if (mexcApiClient.hasValidCredentials()) {
                logger.info('🔍 Testing MEXC API connection...');

                // Try to get balance to test connection
                const balance = await mexcApiClient.getBalance();
                logger.info(`✅ MEXC API connected successfully. Balance: ${balance.total} USDT`);

                // Also test futures balance
                try {
                    const futuresBalance = await mexcApiClient.getFuturesBalance();
                    logger.info(`📊 Futures Balance: ${futuresBalance.totalWalletBalance} USDT`);
                } catch (futuresError) {
                    logger.warn(`⚠️ Futures balance check failed: ${futuresError.message}`);
                }
            } else {
                logger.info('⚠️ MEXC API credentials not configured');
                logger.info('   Please configure your MEXC API credentials in the dashboard');
            }
        } catch (error) {
            logger.error(`❌ MEXC API connection failed: ${error.message}`);
            logger.info('   Please check your MEXC API credentials and network connection');
        }
    }, 3000); // Wait 3 seconds after startup for config to load
});

module.exports = app;
